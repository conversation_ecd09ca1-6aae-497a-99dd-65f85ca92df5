package com.jri.biz.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
// resolved: 移除证书管理相关的常量import
// import com.jri.biz.constants.LicenseBizStatusConstant;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.domain.common.CustomerAgent;
import com.jri.biz.domain.common.ECharsPair;
import com.jri.biz.domain.convert.CustomerInformationConvert;
import com.jri.biz.domain.entity.*;
// resolved: 移除财务和证书管理相关的import
// import com.jri.biz.domain.entity.finance.FinancePayment;
// import com.jri.biz.domain.entity.license.LicenseBizTask;
import com.jri.biz.domain.request.*;
import com.jri.biz.domain.vo.*;
import com.jri.biz.mapper.*;
// resolved: 移除财务和证书管理相关的mapper和service import
// import com.jri.biz.mapper.finance.FinancePaymentMapper;
// import com.jri.biz.mapper.license.LicenseBizTaskMapper;
// import com.jri.biz.service.fund.TurnoverStatementService;
import com.jri.biz.utils.CustomerDataScopeSqlUtil;
import com.jri.common.annotation.Excel;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.entity.SysDept;
import com.jri.common.core.domain.entity.SysDictData;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.exception.CheckedException;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.poi.ExcelUtil;
import com.jri.common.utils.poi.NewExcelUtil;
import com.jri.common.utils.uuid.Seq;
import com.jri.message.constants.MessageConstant;
import com.jri.message.domain.request.MessageDetailForm;
import com.jri.message.domain.vo.ExtraDataVO;
import com.jri.message.service.MessageDetailService;
import com.jri.system.mapper.SysUserMapper;
import com.jri.system.service.ISysDictTypeService;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.net.ssl.HttpsURLConnection;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerInformationService extends ServiceImpl<CustomerInformationMapper, CustomerInformation> {

    private static final Logger log = LoggerFactory.getLogger(CustomerInformationService.class);
    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private CustomerContactMapper customerContactMapper;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    @Resource
    private CustomerBankService customerBankService;

    @Resource
    private CustomerBusinessInformationService customerBusinessInformationService;

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerPersonalizedInformationService customerPersonalizedInformationService;

    // resolved: 移除进度管理直接操作，应该通过服务层调用
    // @Resource
    // private BizProgressMapper bizProgressMapper;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerDiscardRecordMapper customerDiscardRecordMapper;

    // resolved: 移除合同管理相关的mapper注入
    // @Resource
    // private CustomerContractMapper customerContractMapper;

    // resolved: 移除财务相关的mapper注入
    // @Resource
    // private FinancePaymentMapper financePaymentMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ISysDictTypeService sysDictTypeService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    // resolved: 移除财务相关的service注入
    // @Resource
    // TurnoverStatementService turnoverStatementService;

    @Resource
    MessageDetailService messageDetailService;

    @Resource
    private BasicCompanyMapper basicCompanyMapper;

    @Resource
    private CusCustomerOrClueMapper CusCustomerOrClueMapper;

    // resolved: 移除证书管理相关的mapper注入
    // @Resource
    // private CustomerLicenseMapper customerLicenseMapper;

    @Resource
    private DealNameAndDeptService dealNameAndDeptService;

    @Resource
    private CustomerUserLinkRecordMapper customerUserLinkRecordMapper;

    @Resource
    private UserDeptService userDeptService;

    @Resource
    private CustomerBankCommonService customerBankCommonService;

    // resolved: 移除订单管理相关的mapper注入
    // @Resource
    // private OrderMapper orderMapper;

    // resolved: 移除证书管理相关的mapper注入
    // @Resource
    // private LicenseBizTaskMapper licenseBizTaskMapper;

    @Resource
    private CustomerUserRelateRecordService customerUserRelateRecordService;

    DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");

    @Value("${oss.endpoint}")
    String endpoint;
    @Value("${oss.accessKeyId}")
    String accessKeyId;
    @Value("${oss.accessKeySecret}")
    String accessKeySecret;
    @Value("${oss.bucketName}")
    String bucketName;


    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerInformationListVO> listPage(CustomerInformationQuery query) {
        var page = new Page<CustomerInformationListVO>(query.getPageNum(), query.getPageSize());
        String dataScopeSql = CustomerDataScopeSqlUtil.getDataScopeSql(null, null);
        query.setDataScopeSql(dataScopeSql);
        var result = getBaseMapper().listPage(page, query);
        result.getRecords().parallelStream().forEach(info -> {

            LambdaQueryWrapper<BasicCompany> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BasicCompany::getName, info.getBranchOffice());
            wrapper.last("limit 1");
            BasicCompany basicCompany = basicCompanyMapper.selectOne(wrapper);
            if (null != basicCompany) {
                info.setCompanyPerson(basicCompany.getContacts());
                info.setCompanyPhone(basicCompany.getPhone());
                info.setCompanyAddress(basicCompany.getAddress());
            }

            var customerId = info.getCustomerId();
            var vo = customerPersonalizedInformationService.getByCiId1(customerId);
            vo.ifPresent(cpi -> {
                info.setTags(cpi.getTags());
                info.setTypes(cpi.getTypes());
                info.setPersonality(cpi.getPersonality());
                info.setAgeLevel(cpi.getAgeLevel());
                info.setPersonalityComplement(cpi.getPersonalityComplement());
                info.setCollectionRequirement(cpi.getCollectionRequirement());
                info.setDealRequirement(cpi.getDealRequirement());
                info.setBillingDemand(cpi.getBillingDemand());
            });
            // resolved: 移除财务相关的费用计算逻辑
            // info.setMonthlyFee(turnoverStatementService.monthTurnoverByCustomerId(customerId, null));
            // info.setBookkeepingMonthlyFee(turnoverStatementService.monthTurnoverByCustomerId(customerId, "代理记账"));
            // info.setAddressMonthlyFee(turnoverStatementService.monthTurnoverByCustomerId(customerId, "地址费"));
            // info.setArrears(turnoverStatementService.getArrears(customerId));
            // resolved: 移除合同管理相关的到期时间查询
            //查询最近合同到期
            // info.setDueDate(customerContractMapper.getLatestExpireTime(customerId));

            // 银行信息
            Long bankId = info.getBankId();
            if (ObjectUtil.isNotNull(bankId)) {
                var customerBankWrapper = new LambdaQueryWrapper<CustomerBankCommon>();
                customerBankWrapper.eq(CustomerBankCommon::getBankId, bankId);
                info.setCommonListSize(customerBankCommonService.count(customerBankWrapper));
            }

            // resolved: 移除证书管理相关的许可证信息查询
            // 许可证信息
            // List<CustomerLicenseVO> licenseVOList = customerLicenseMapper.getDetailByCiId(info.getCustomerId());
            // info.setLicenseSize(licenseVOList.size());

            // 财税顾问、客户成功、主办会计、开票员 部门查询
            Map<Long, SysUser> sysUserMap = new HashMap<>();
            Map<Long, SysDept> sysDeptMap = userDeptService.getSysDeptMap();
            if (ObjectUtil.isNotEmpty(sysDeptMap)) {
                Long mangerUserId = info.getMangerUserId();
                Long counselorUserId = info.getCounselorUserId();
                Long customerSuccessUserId = info.getCustomerSuccessUserId();
                Long sponsorAccountingUserId = info.getSponsorAccountingUserId();
                info.setManagerUserName(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, mangerUserId));
                info.setCounselorUserName(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, counselorUserId));
                info.setCustomerSuccessUserName(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, customerSuccessUserId));
                info.setSponsorAccountingUserName(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, sponsorAccountingUserId));
            }

        });
        return result;
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerInformationVO getDetailById(Long id) {
        CustomerInformationVO vo = getBaseMapper().getDetailById(id);
        if (ObjectUtil.isNull(vo)) {
            throw new ServiceException("客户信息不存在或已删除");
        }
        //查询附件
        vo.setDiscardTime(customerDiscardRecordMapper.getRecentDiscardTime(id));
        List<CommonBizFile> interviewFileList = commonBizFileService.selectByMainIdAndBizType(vo.getCustomerId(), BizType.INTERVIEW);
        List<CommonBizFile> businessFileList = commonBizFileService.selectByMainIdAndBizType(vo.getCustomerId(), BizType.BUSINESS_LICENSE);
        vo.setInterviewFileList(interviewFileList);
        vo.setBusinessFileList(businessFileList);
        // 来自转化客户
        List<String> list = CusCustomerOrClueMapper.getCompanyNameByCiId(vo.getCustomerId());
        vo.setClientName(String.join(",", list));

        LambdaQueryWrapper<BasicCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicCompany::getName, vo.getBranchOffice());
        wrapper.last("limit 1");
        BasicCompany basicCompany = basicCompanyMapper.selectOne(wrapper);
        if (null != basicCompany) {
            vo.setCompanyPerson(basicCompany.getContacts());
            vo.setCompanyPhone(basicCompany.getPhone());
            vo.setCompanyAddress(basicCompany.getAddress());
        }
        // 财税顾问、客户成功、主办会计、开票员 部门查询
        Map<Long, SysUser> sysUserMap = new HashMap<>();
        Map<Long, SysDept> sysDeptMap = userDeptService.getSysDeptMap();
        if (ObjectUtil.isNotEmpty(sysDeptMap)) {
            Long mangerUserId = vo.getMangerUserId();
            Long counselorUserId = vo.getCounselorUserId();
            Long customerSuccessUserId = vo.getCustomerSuccessUserId();
            Long sponsorAccountingUserId = vo.getSponsorAccountingUserId();
            vo.setManger(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, mangerUserId));
            vo.setCounselor(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, counselorUserId));
            vo.setCustomerSuccess(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, customerSuccessUserId));
            vo.setSponsorAccounting(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, sponsorAccountingUserId));
        }


        return vo;
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdate(CustomerInformationForm form) {
        boolean containsSpecialChars = form.getCustomerName().matches(".*[%_\\[\\]'\"]+.*");
        if (containsSpecialChars) {
            throw new CheckedException("客户名称有特殊字符");
        }

        // 校验企业名称唯一
        LambdaQueryWrapper<CustomerInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInformation::getCustomerName, form.getCustomerName());
        if (null != form.getCustomerId()) {
            wrapper.ne(CustomerInformation::getCustomerId, form.getCustomerId());
        }
        if (count(wrapper) > 0) {
            throw new CheckedException("企业名称已存在");
        }

        var customerInformation = CustomerInformationConvert.INSTANCE.convert(form);
        if (ObjectUtil.isNotEmpty(form.getCompanyIdentificationList())) {
            customerInformation.setCompanyIdentification(String.join(",", form.getCompanyIdentificationList()));
        }
        Long oldId = null;
        //新增
        String content = "编辑";
        CustomerInformation oldCustomerInformation = new CustomerInformation();
        if (form.getCustomerId() == null) {
            content = "新增";
            //生成编号 C202300001
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
            String formattedDate = currentDate.format(formatter);
            StringBuilder code = new StringBuilder().append("C").append(formattedDate);
            String num = customerInformationMapper.selectMaxCode(String.valueOf(code));
            if (ObjectUtils.isEmpty(num)) {
                num = "00001";
            }
            code.append(num);
            customerInformation.setCustomerNo(String.valueOf(code));
            customerInformation.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            customerInformation.setCreateTime(LocalDateTime.now());
        } else {
            customerInformation.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
            customerInformation.setUpdateTime(LocalDateTime.now());
            oldCustomerInformation = getByCustomerId(form.getCustomerId());
            customerInformation.setCustomerNo(oldCustomerInformation.getCustomerNo());
            oldId = form.getCustomerId();
        }
        //应收台账那边4种业务员 要求展示人员的最后两级部门+人员姓名,建议在新增的时候按照格式存进数据库
        customerInformation = dealNameAndDeptService.dealNameandDept(customerInformation, form.getMangerUserId(), form.getCounselorUserId(), form.getCustomerSuccessUserId(), form.getSponsorAccountingUserId());

        if (saveOrUpdate(customerInformation)) {
            // 关联时间更新
            linkTimeHandle(customerInformation, oldCustomerInformation);
            // 发送消息
            if (oldId != null) {
                //修改 客户信息变更消息要发送
                userChangeHandle(customerInformation, oldCustomerInformation);
            } else {
                //新增  客户信息变更消息不用发送（产品说的********)
                sendMessage(customerInformation, oldCustomerInformation, null);
            }

            completenessService.UpdateCompleteness(customerInformation.getCustomerId());

            customerChangeRecordService.sendChangeMessage(customerInformation);

            updateById(customerInformation);

            //插入联系人
            var customerId = customerInformation.getCustomerId();
            var contactPerson = form.getContactPerson();
            var phone = form.getContactPhone();
            //联系人
            if (ObjectUtil.isNull(form.getContractMainId()) &&
                    (ObjectUtil.isNotEmpty(contactPerson) || ObjectUtil.isNotEmpty(phone))) {
                var contact = new CustomerContact();
                contact.setCiId(customerId);
                contact.setPhone(phone);
                contact.setName(contactPerson);
                contact.setWechat(form.getWx());
                BeanUtil.copyProperties(form, contact);
                contact.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
                customerContactService.save(contact);
                customerInformation.setContractMainId(contact.getId());
                updateById(customerInformation);
            } else if (ObjectUtil.isNotNull(form.getContractMainId())) {
                CustomerContact customerContact = customerContactMapper.selectById(form.getContractMainId());
                customerContact.setPhone(phone);
                customerContact.setName(contactPerson);
                customerContact.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
                customerContactService.updateById(customerContact);
            }

            //保存营业执照
            commonBizFileService.deleteByMainIdAndBizType(form.getCustomerId(), BizType.INTERVIEW);
            List<CommonBizFile> interviewFileList = form.getInterviewFileList();
            if (ObjectUtil.isNotEmpty(interviewFileList)) {
                interviewFileList.forEach(item -> {
                    item.setMainId(customerId);
                    item.setBizType(BizType.INTERVIEW);
                });
                commonBizFileService.saveBatch(interviewFileList);
            }
            CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
            changeRecordForm.setCiId(customerId);
            changeRecordForm.setContent(content);
            changeRecordForm.setInfoSection("企业信息");
            customerChangeRecordService.add(changeRecordForm);
            return customerId;
        } else {
            return -1L;
        }
    }

    /**
     * 抽象关联时间处理方法
     *
     * @param nowDate     现在日期
     * @param now         现在时间
     * @param setLinkTime 设置关联时间方法
     * @param newUserId   新用户id
     * @param oldUserId   旧用户id
     * @param customerId  客户id
     * @param role        关联角色
     */
    private void linkTimeHandle(LocalDate nowDate, LocalDateTime now, LocalDateTime oldLinkTime,
                                Consumer<LocalDateTime> setLinkTime, Long newUserId, Long oldUserId, Long customerId,
                                String role) {
        if (ObjectUtil.notEqual(newUserId, oldUserId)) {
            customerUserRelateRecordService.createRecord(customerId, newUserId, role);
        }

        if (ObjectUtil.isNotNull(newUserId)) {
            // 新旧不相同更新关联时间
            if (ObjectUtil.notEqual(newUserId, oldUserId)) {
                setLinkTime.accept(now);
                // 记录关联时间
//                linkRecordHandle(nowDate, customerId, oldUserId, newUserId, role);
            } else {
                setLinkTime.accept(oldLinkTime);
            }
        } else {
            // 没有去除关联时间
            setLinkTime.accept(null);
            // 记录关联时间
//            linkRecordHandle(nowDate, customerId, oldUserId, newUserId, role);
        }
    }

    /**
     * 关联时间处理
     *
     * @param newInfo 新客户信息
     * @param oldInfo 老客户信息
     */
    private void linkTimeHandle(CustomerInformation newInfo, CustomerInformation oldInfo) {
        final var now = LocalDateTime.now();
        final var nowDate = now.toLocalDate();
        Long customerId = newInfo.getCustomerId();
        // 客户经理
        linkTimeHandle(nowDate, now, oldInfo.getMangerLinkTime(), newInfo::setMangerLinkTime, newInfo.getMangerUserId(), oldInfo.getMangerUserId(), customerId, "manager");
        // 开票员
        linkTimeHandle(nowDate, now, oldInfo.getCounselorLinkTime(), newInfo::setCounselorLinkTime, newInfo.getCounselorUserId(), oldInfo.getCounselorUserId(), customerId, "counselor");
        // 客户成功
        linkTimeHandle(nowDate, now, oldInfo.getCustomerSuccessLinkTime(), newInfo::setCustomerSuccessLinkTime, newInfo.getCustomerSuccessUserId(), oldInfo.getCustomerSuccessUserId(), customerId, "customer_success");
        // 主办会计
        linkTimeHandle(nowDate, now, oldInfo.getSponsorAccountingLinkTime(), newInfo::setSponsorAccountingLinkTime, newInfo.getSponsorAccountingUserId(), oldInfo.getSponsorAccountingUserId(), customerId, "sponsor_accounting");
    }

    /**
     * 人员关联记录处理
     *
     * @param nowDate    现在日期
     * @param customerId 客户id
     * @param oldUserId  旧用户id
     * @param newUserId  新用户id
     * @param role       角色
     */
    private void linkRecordHandle(LocalDate nowDate, Long customerId, Long oldUserId, Long newUserId, String role) {
        if (ObjectUtil.isNull(oldUserId) && ObjectUtil.isNotNull(newUserId)) {
            // 新增
            customerUserLinkRecordMapper.insert(
                    CustomerUserLinkRecord.builder().role(role).linkTime(nowDate.withDayOfMonth(1)).customerId(customerId).userId(newUserId).build()
            );
            return;
        }
        CustomerUserLinkRecord linkRecord = customerUserLinkRecordMapper.getByLinkRecord(customerId, role, oldUserId);
        if (ObjectUtil.isNotNull(linkRecord)) {
            // 这个月不算 算上一个月
            LocalDate lastDate = nowDate.plusMonths(-1);
            // 如果和开始时间月份一样那不算关联直接删除记录
            if (lastDate.getYear() == linkRecord.getLinkTime().getYear() && lastDate.getMonthValue() == linkRecord.getLinkTime().getMonthValue()) {
                customerUserLinkRecordMapper.deleteById(linkRecord.getId());
            }
            // 更新
            linkRecord.setUnlinkTime(lastDate.withDayOfMonth(lastDate.lengthOfMonth()));
            customerUserLinkRecordMapper.updateById(linkRecord);
        }
        if (ObjectUtil.isNotNull(newUserId)) {
            // 新增
            customerUserLinkRecordMapper.insert(
                    CustomerUserLinkRecord.builder().role(role).linkTime(nowDate.withDayOfMonth(1)).customerId(customerId).userId(newUserId).build()
            );
        }
    }

    // resolved: 简化消息通知具体实现，改为事件发布模式
    /**
     * 根据新旧客户信息的变更，向客户关联的用户发送消息。
     * 如果指定了角色，则发送关联人员变更的通知。
     * 如果所有用户都发生了变更（通过集合大小大于3判断），
     * 则不发送消息。
     *
     * @param newInfo 新的客户信息
     * @param oldInfo 旧的客户信息
     * @param roles   变更的角色，如果有的话
     */
    public void sendMessage(CustomerInformation newInfo, CustomerInformation oldInfo, String roles) {
        // resolved: 简化为事件发布，具体的消息发送逻辑由消息服务处理
        if (ObjectUtil.isAllEmpty(newInfo, oldInfo)) {
            return;
        }

        // TODO: 发布客户信息变更事件，由事件监听器处理具体的消息发送逻辑
        // applicationEventPublisher.publishEvent(new CustomerInfoChangeEvent(newInfo, oldInfo, roles));

        log.info("Customer information changed: customerNo={}, customerName={}, roles={}",
                newInfo.getCustomerNo(), newInfo.getCustomerName(), roles);
    }

    // resolved: 简化消息通知具体实现，移除具体的消息发送逻辑
    /**
     * 如果用户 ID 发生变化，则向新用户 ID 发送消息详
     * 情表单，否则返回旧用户 ID。如果 newUserId 为空，
     * 则视为没有变化。
     *
     * @param oldUserId   原用户 ID
     * @param newUserId   新用户 ID，用于处理消息
     * @param role        消息内容中的用户角色
     * @param messageForm 包含消息详情的表单
     * @return 如果没有变化则返回 oldUserId，
     * 否则在发送消息后返回 null
     */
    // private Long sendHandleUserMessage(Long oldUserId, Long newUserId, String role, MessageDetailForm messageForm) {
    //     // 没有变化返回oldUserId 通知其变化
    //     if (ObjectUtil.equals(oldUserId, newUserId) || ObjectUtil.isNull(newUserId)) {
    //         return oldUserId;
    //     }
    //     messageForm.setRecipients(Collections.singletonList(newUserId));
    //     messageForm.getContentParam().put("role", role);
    //     try {
    //         messageDetailService.send(messageForm);
    //     } catch (Exception e) {
    //         log.error("消息发送失败:{}", e.getMessage());
    //     }
    //     return null;
    // }


    //0--4种人没变   1-- 4种人变动了
    public void userChangeHandle(CustomerInformation newInfo, CustomerInformation oldInfo) {

        if (newInfo == null || oldInfo == null) {
            return;
        }
        String roles;
        List<String> sendContentOfAssociate = new ArrayList<>();
        boolean flag = false;
        if (!Objects.equals(newInfo.getMangerUserId(), oldInfo.getMangerUserId())) {
            sendContentOfAssociate.add("财税顾问");
            flag = true;
        }
        if (!Objects.equals(newInfo.getCounselorUserId(), oldInfo.getCounselorUserId())) {
            sendContentOfAssociate.add("开票员");
            flag = true;
        }
        if (!Objects.equals(newInfo.getCustomerSuccessUserId(), oldInfo.getCustomerSuccessUserId())) {
            sendContentOfAssociate.add("客户成功");
            flag = true;
        }
        if (!Objects.equals(newInfo.getSponsorAccountingUserId(), oldInfo.getSponsorAccountingUserId())) {
            sendContentOfAssociate.add("主办会计");
            flag = true;
        }
        if (flag) {
            roles = String.join("/", sendContentOfAssociate);
            sendMessage(newInfo, oldInfo, roles);
        }
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerInformationForm form) {
        CustomerInformation customerInformation = null;
        //联系人
        if (!form.getContactPerson().isBlank() || !form.getContactPhone().isBlank()) {
            //查询现有联系人
            var wrapper = new LambdaQueryWrapper<CustomerContact>();
            wrapper.eq(CustomerContact::getId, form.getCustomerId());
            wrapper.eq(CustomerContact::getName, form.getContactPerson());
            wrapper.eq(CustomerContact::getPhone, form.getContactPhone());
            CustomerContact customerContact;
            try {
                customerContact = customerContactMapper.selectOne(wrapper);
            } catch (Exception e) {
                throw new ServiceException("查询联系人有多个，请填写手机号");
            }
            if (customerContact == null) {
                //新建联系人
                CustomerContact customerContact1 = new CustomerContact();
                customerContact1.setPhone(form.getContactPhone());
                customerContact1.setName(form.getContactPerson());
                CustomerContactForm customerContactForm = new CustomerContactForm();
                BeanUtil.copyProperties(customerContact1, customerContactForm);
                var concatId = customerContactService.saveOrUpdate(customerContactForm);
                customerInformation = CustomerInformationConvert.INSTANCE.convert(form);
                customerInformation.setContractMainId(concatId);
            }

        } else {
            customerInformation = CustomerInformationConvert.INSTANCE.convert(form);
        }
        //不收费原因 客户开票资料 下户表
        // 删除原附件
        commonBizFileService.deleteByMainIdAndBizType(form.getCustomerId(), BizType.NOFEE_REASON);
        commonBizFileService.deleteByMainIdAndBizType(form.getCustomerId(), BizType.INVOICE_INFO);
        commonBizFileService.deleteByMainIdAndBizType(form.getCustomerId(), BizType.INTERVIEW);
        //保存附件
        List<CommonBizFile> interviewFileList = form.getInterviewFileList();
        if (ObjectUtil.isNotEmpty(interviewFileList)) {
            interviewFileList.forEach(item -> {
                item.setMainId(form.getCustomerId());
                item.setBizType(BizType.INTERVIEW);
            });
            commonBizFileService.saveBatch(interviewFileList);
        }
        return updateById(customerInformation);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        customerInformationMapper.delete(id);
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        ids.forEach(item -> {
            // resolved: 移除合同管理相关的删除检查逻辑
            // LambdaQueryWrapper<CustomerContract> wrapper = new LambdaQueryWrapper<>();
            // wrapper.eq(CustomerContract::getCiId, item);
            // if (customerContractMapper.selectCount(wrapper) > 0) {
            //     throw new ServiceException("选中客户中存在已绑定合同客户,无法删除");
            // }
            // resolved: 移除财务相关的删除检查逻辑
            // CustomerInformation byId = getById(item);
            // LambdaQueryWrapper<FinancePayment> financeWrapper = new LambdaQueryWrapper<>();
            // financeWrapper.eq(FinancePayment::getCustomerNo, byId.getCustomerNo());
            // if (financePaymentMapper.selectCount(financeWrapper) > 0) {
            //     throw new ServiceException("选中客户中存在已绑定账单客户,无法删除");
            // }
            removeById(item);
        });
    }

    public CustomerInformationVO getDetailByCiId(Long ciId) {
        CustomerInformationVO customerInformationVO = new CustomerInformationVO();
        return customerInformationVO;
    }

    // resolved: 移除导入导出功能耦合，应该由专门的导入导出服务处理
    /**
     * 客户信息导入
     *
     * @param is         导入文件流
     * @param progressId 流程id
     * @param coverFlag  是否覆盖
     */
    // @Async
    // public void upload(InputStream is, Long progressId, Boolean coverFlag) {
        // var eu = new NewExcelUtil<>(CustomerInformationExcel.class, coverFlag);
        // var progress = bizProgressMapper.selectById(progressId);
        // TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        // try {
        //     String[] titles = {"基本户账号", "结算卡", "回单卡", "回单卡密码", "一般户银行开户行", "一般户账号", "一般户回单卡", "一般户回单卡密码", "企业状态", "银行开户许可证", "机构代码证", "企业编号",
        //             "所属分公司", "公司章程", "股东会决议", "地址", "变更信息", "创建者", "身份证件", "客户开票资料", "客户经理1", "成立日期", "社会信用代码", "营业结束日期", "营业执照", "经营范围", "企业性质",
        //             "客户名称", "手机", "基本户开户银行", "注册资料", "组织机关代码", "核准日期", "营业开始时间", "注册号", "行业", "登记机关", "注册资金", "联系方式", "注册地址", "法定代表人", "公司类型", "线索来源",
        //             "联系电话", "实际经营地址", "客户类型", "信息来源", "开票员", "地址是否我方提供", "票据张数", "a联系人电话", "累计记账收款", "累计合同金额", "客户经理", "客服", "会计", "企业名称"};
        //     var list = eu.importExcel(is, titles);
        //     if (ObjectUtil.isEmpty(list)) {
        //         throw new ServiceException("导入内容为空");
        //     }
        //     // 校验必填项
        //     for (CustomerInformationExcel it : list) {
        //         if (ObjectUtil.isEmpty(it.getCustomerName())) {
        //             throw new ServiceException("存在企业名称未填写");
        //         }
        //     }
        //     // 校验重复企业名称
        //     List<String> collect = list.stream()
        //             .map(CustomerInformationExcel::getCustomerName)
        //             .distinct()
        //             .toList();
        //     if (collect.size() != list.size()) {
        //         throw new ServiceException("存在重复企业名称");
        //     }

        //     for (CustomerInformationExcel it : list) {
        //         log.info("************客户名称*************");
        //         log.info(it.getCustomerName());
        //         if (it.getCustomerName().contains(":") || it.getCustomerName().contains("：")) {
        //             continue;
        //         }
        //         //保存基础信息
        //         var infoForm = new CustomerInformationForm();
        //         BeanUtil.copyProperties(it, infoForm);
        //         if (ObjectUtil.isEmpty(infoForm.getCustomerName())) {
        //             throw new ServiceException("存在企业名称未填写");
        //         }
        //         // 覆盖查询客户id
        //         CustomerInformation customerInfo = getBaseMapper().getCustomerByCustomerName(infoForm.getCustomerName());
        //         boolean otherInfoCoverFlag = coverFlag;
        //         if (coverFlag) {
        //             if (ObjectUtil.isNull(customerInfo)) {
        //                 customerInfo = new CustomerInformation();
        //                 otherInfoCoverFlag = false;
        //             } else {
        //                 infoForm.setCustomerId(customerInfo.getCustomerId());
        //                 infoForm.setContractMainId(customerInfo.getContractMainId());
        //             }
        //         } else {
        //             if (ObjectUtil.isNotNull(customerInfo)) {
        //                 throw new ServiceException(infoForm.getCustomerName() + "客户已存在");
        //             }
        //         }
        //         infoForm.setContactPerson(it.getName());
        //         infoForm.setContactPhone(it.getPhone());
        //         // 客户经理
        //         customerInfoUserIdFill(Optional.ofNullable(customerInfo).map(CustomerInformation::getMangerUserId).orElse(null), infoForm.getManger(), infoForm::setMangerUserId);
        //         // 主办会计
        //         customerInfoUserIdFill(Optional.ofNullable(customerInfo).map(CustomerInformation::getSponsorAccountingUserId).orElse(null), infoForm.getSponsorAccounting(), infoForm::setSponsorAccountingUserId);
        //         // 客户成功
        //         customerInfoUserIdFill(Optional.ofNullable(customerInfo).map(CustomerInformation::getCustomerSuccessUserId).orElse(null), infoForm.getCustomerSuccess(), infoForm::setCustomerSuccessUserId);
        //         // 开票员
        //         customerInfoUserIdFill(Optional.ofNullable(customerInfo).map(CustomerInformation::getCounselorUserId).orElse(null), infoForm.getCounselor(), infoForm::setCounselorUserId);
        //
        //         var customerId = addOrUpdate(infoForm);
        //         if (customerId < 0L) {
        //             throw new ServiceException("导入基础信息失败：保存基础信息失败");
        //         }
        //
        //         //保存银行信息
        //         bankInfoImportHandle(it, customerId, otherInfoCoverFlag);
        //
        //         //保存工商信息
        //         businessInfoHandle(it, customerId, otherInfoCoverFlag);
        //
        //     }
        //     progress.setStatus(1);//导入完成
        //     dataSourceTransactionManager.commit(transactionStatus);
        //     log.info("********************************导入成功****************************************************");
        // } catch (Exception e) {
        //     log.info("********************************导入失败****************************************************");
        //     String message = e.getMessage();
        //     String regex = "[\u4e00-\u9fa5]+";
        //     if (!message.matches(".*" + regex + ".*")) {
        //         message = "未知异常请联系管理员:" + message;
        //     }
        //     progress.setReason(message);
        //     progress.setStatus(2);//导入失败
        //     log.warn("导入失败", e);
        //     dataSourceTransactionManager.rollback(transactionStatus);
        //     throw new ServiceException(e.getMessage());
        // } finally {
        //     log.info("********************************导入结束****************************************************");
        //     bizProgressMapper.updateById(progress);
        // }
    // }

    /**
     * 工商信息导入处理
     *
     * @param it         导入数据
     * @param customerId 客户id
     */
    private void businessInfoHandle(CustomerInformationExcel it, Long customerId, Boolean coverFlag) {
        var businessInformation = new CustomerBusinessInformationForm();
        if (coverFlag) {
            // 不拷贝文件只拷贝数据
            var bizInfoDetail = customerBusinessInformationService.getBaseMapper().getDetailByCiId(customerId);
            if (ObjectUtil.isNotNull(bizInfoDetail)) {
                BeanUtil.copyProperties(bizInfoDetail, businessInformation, CopyOptions.create().ignoreNullValue());
            }
        }
        businessInformation.setCoverFlag(coverFlag);
        BeanUtils.copyProperties(it, businessInformation);
        businessInformation.setCiId(customerId);
        if (ObjectUtil.isNotEmpty(it.getEstablishDate())) {
            businessInformation.setEstablishDate(dateformat.format(it.getEstablishDate()));
        }
        if (ObjectUtil.isNotEmpty(it.getOpenDate())) {
            businessInformation.setOpenDate(dateformat.format(it.getOpenDate()));
        }
        if (ObjectUtil.isNotEmpty(it.getOpenEnd())) {
            businessInformation.setOpenEnd(dateformat.format(it.getOpenEnd()));
        }
        if (ObjectUtil.isNotEmpty(it.getApprovalDate())) {
            businessInformation.setApprovalDate(dateformat.format(it.getApprovalDate()));
        }

        // 营业执照
        businessInformation.setBusinessFileList(importUrlHandle(it.getBusinessLicenseStr()));
        // 注册资料
        businessInformation.setRegistrationInformationFileList(importUrlHandle(it.getRegistrationInformationFileStr()));
        // 公司章程
        businessInformation.setBusinessConstitutionFileList(importUrlHandle(it.getBusinessConstitutionFileStr()));
        // 股东会决议
        businessInformation.setShareholderCommitteeRessolutionFileList(importUrlHandle(it.getShareholderCommitteeRessolutionFileStr()));
        // 地址
        businessInformation.setAdressFileList(importUrlHandle(it.getAdressFileStr()));
        // 身份证件
        businessInformation.setIdentityDocumentFileList(importUrlHandle(it.getIdentityDocumentFileStr()));

        //保存
        customerBusinessInformationService.saveOrUpdate(businessInformation);
    }

    /**
     * 银行信息导入处理
     *
     * @param it         导入信息
     * @param customerId 客户id
     */
    private void bankInfoImportHandle(CustomerInformationExcel it, Long customerId, Boolean coverFlag) {
        var bank = new CustomerBankForm();
        if (coverFlag) {
            var bankDetail = customerBankService.getBaseMapper().getDetailByCiId(customerId);
            if (ObjectUtil.isNotNull(bankDetail)) {
                BeanUtil.copyProperties(bankDetail, bank, CopyOptions.create().ignoreNullValue());
            }
            bank.setCoverFlag(coverFlag);
        }
        BeanUtils.copyProperties(it, bank);
        bank.setCiId(customerId);
        // 变更信息
        bank.setChangeInfoFileList(importUrlHandle(it.getBusinessChangeInfoFileStr()));
        // 银行开户许可证
        bank.setAccountOpenFileList(importUrlHandle(it.getAccountOpenFileStr()));

        if ("已办理".equals(it.getReceiptCardAccount())) {
            bank.setReceiptCardFlag("1");
            bank.setReceiptCardType("卡");
        } else if ("未办理".equals(it.getReceiptCardAccount())) {
            bank.setReceiptCardFlag("0");
        }
        bank.setReceiptCardAccount(null);

        String commonBankAccount = it.getCommonBankAccount();// 一般户账号
        String commonInternetbankAccount = it.getCommonInternetbankAccount();// 一般户回单卡 未办理 - 无；已办理 - 有
        String commonBankName = it.getCommonBankName();// 一般户银行开户行
        String commonReceiptCardPassword = it.getCommonReceiptCardPassword();// 一般户回单卡密码
        if (ObjectUtil.isAllNotEmpty(commonBankAccount, commonInternetbankAccount, commonBankName, commonReceiptCardPassword)) {
            List<CustomerBankCommonForm> commonList = new ArrayList<>();
            CustomerBankCommonForm customerBankCommonForm = new CustomerBankCommonForm();
            customerBankCommonForm.setCommonBankName(commonBankName);
            if ("已办理".equals(commonInternetbankAccount)) {
                customerBankCommonForm.setCommonReceiptCardFlag("1");
            } else if ("未办理".equals(commonInternetbankAccount)) {
                customerBankCommonForm.setCommonReceiptCardFlag("0");
            }
            customerBankCommonForm.setCommonBankAccount(commonBankAccount);
            if (ObjectUtil.isNotEmpty(commonReceiptCardPassword)) {
                customerBankCommonForm.setCommonReceiptCardPassword(commonReceiptCardPassword);
                customerBankCommonForm.setCommonInternetbankType("账号密码");
            }
            commonList.add(customerBankCommonForm);
            bank.setCommonList(commonList);
        }
        customerBankService.addOrUpdate(bank);
    }

    /**
     * 客户信息用户id填充
     *
     * @param oldUserId 旧用户id
     * @param name      用户名
     * @param setUserId set方法
     */
    private void customerInfoUserIdFill(Long oldUserId, String name, Consumer<Long> setUserId) {
        if (StrUtil.isBlank(name)) {
            if (ObjectUtil.isNull(oldUserId)) {
                return;
            }
            setUserId.accept(oldUserId);
            return;
        }
        Long userId = sysUserMapper.getUserIdByName(name);
        if (ObjectUtil.isEmpty(userId)) {
            throw new ServiceException("系统未查询到" + name + "用户信息,请先创建用户");
        }
        setUserId.accept(userId);
    }

    /**
     * 导入文件 url文件处理
     *
     * @param url url
     * @return 文件列表
     */
    private List<CommonBizFile> importUrlHandle(String url) {
        String newUrl = url;
        if (StrUtil.isBlank(url)) {
            return null;
        }
        if (url.contains(",")) {
            String[] split = url.split(",");
            newUrl = split[0];
        }
        if (StrUtil.isBlank(newUrl)) {
            return null;
        }
        CommonBizFile commonBizFile = uploadByUrlOss(newUrl);
        return commonBizFile == null ? null : Collections.singletonList(commonBizFile);
    }

    /**
     * 变更经理
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeManger(ChangeMangerForm form) {
        CustomerInformation oldCustomerInformation = getById(form.getCustomerId());
        getBaseMapper().changeManger(form);
        var info = getById(form.getCustomerId());
        //应收台账那边4种业务员 要求展示人员的最后两级部门+人员姓名,建议在新增的时候按照格式存进数据库
        CustomerAgent mangerCustomerAgent = dealNameAndDeptService.dealPersonNameDept(form.getMangerUserId(), form.getManger());
        info.setMangerAndDept(mangerCustomerAgent.getDeptAndName());

        CustomerAgent counselorCustomerAgent = dealNameAndDeptService.dealPersonNameDept(info.getCounselorUserId(), info.getCounselor());
        info.setCounselorAndDept(counselorCustomerAgent.getDeptAndName());
        CustomerAgent customerSuccessCustomerAgent = dealNameAndDeptService.dealPersonNameDept(info.getCustomerSuccessUserId(), info.getCustomerSuccess());
        info.setCustomerSuccessAndDept(customerSuccessCustomerAgent.getDeptAndName());
        CustomerAgent sponsorAccountingCustomerAgent = dealNameAndDeptService.dealPersonNameDept(info.getSponsorAccountingUserId(), info.getSponsorAccounting());
        info.setSponsorAccountingAndDept(sponsorAccountingCustomerAgent.getDeptAndName());

        //对部门去重
        String seceondDeptArry = mangerCustomerAgent.getDeptIdArry() + "," + counselorCustomerAgent.getDeptIdArry()
                + "," + customerSuccessCustomerAgent.getDeptIdArry() + "," + sponsorAccountingCustomerAgent.getDeptIdArry();
        String dealRepeted = DealNameAndDeptService.dealRepet(seceondDeptArry);

        info.setSeceondDeptArry(dealRepeted);
        linkTimeHandle(info, oldCustomerInformation);
        updateById(info);
        userChangeHandle(info, oldCustomerInformation);

    }

    /**
     * 客户统计
     */
    public CustomerInformationStatisticsVO statistics() {
        CustomerInformationStatisticsVO res = new CustomerInformationStatisticsVO();
        // 客户状态
        List<SysDictData> customerStatusDictData = sysDictTypeService.selectDictDataByType("customer_status");
        if (ObjectUtil.isNotEmpty(customerStatusDictData)) {
            List<ECharsPair<String, Long>> list = new ArrayList<>();
            customerStatusDictData.forEach(item -> list.add(new ECharsPair<>(item.getDictValue(), getBaseMapper().getCountByCustomerStatus(item.getDictValue()))));
            res.setCustomerStatusList(list);
        }
        // 企业性质
        List<SysDictData> customerPropertySysDictData = sysDictTypeService.selectDictDataByType("customer_property");
        if (ObjectUtil.isNotEmpty(customerPropertySysDictData)) {
            List<ECharsPair<String, Long>> list = new ArrayList<>();
            customerPropertySysDictData.forEach(item -> list.add(new ECharsPair<>(item.getDictValue(), getBaseMapper().getCountByCustomerProperty(item.getDictValue()))));
            res.setCustomerPropertyList(list);
        }
        // 企业认定
        List<SysDictData> companyIdentificationSysDictData = sysDictTypeService.selectDictDataByType("company_identification");
        if (ObjectUtil.isNotEmpty(companyIdentificationSysDictData)) {
            List<ECharsPair<String, Long>> list = new ArrayList<>();
            companyIdentificationSysDictData.forEach(item -> list.add(new ECharsPair<>(item.getDictValue(), getBaseMapper().getCountByIdentification(item.getDictValue()))));
            res.setCompanyIdentificationList(list);
        }

        return res;
    }

    /**
     * 主办会计/财税顾问/客户成功变更
     *
     * @param form 表单
     */
    public void changePerson(ChangePersonForm form) {
        Integer type = form.getType();
        CustomerInformation oldCustomerInformation = getById(form.getCustomerId());
        if (type == 0) {
            getBaseMapper().changeSponsorAccounting(form);
        } else if (type == 1) {
            getBaseMapper().changeCounselor(form);
        } else if (type == 2) {
            getBaseMapper().changeCustomerSuccess(form);
        }
        var info = getById(form.getCustomerId());
        linkTimeHandle(info, oldCustomerInformation);
        userChangeHandle(info, oldCustomerInformation);
    }

    public IPage<AssociatedCustomerInformationListVO> getAssociatedCustomerInformationList(CustomerInformationQuery query) {
        var page = new Page<CustomerInformationListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().getAssociatedCustomerInformationList(page, query);
    }

    public CustomerInformation getByCustomerNo(String customerNo) {
        LambdaQueryWrapper<CustomerInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInformation::getCustomerNo, customerNo);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }

    public CustomerInformation getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInformation::getCustomerId, customerId);
        return getOne(wrapper);
    }

    // 从url获取附件上传到oss
    public CommonBizFile uploadByUrlOss(String strUrl) {
        HttpsURLConnection conn = null;
        try {
            URL url = new URL(strUrl);
            conn = (HttpsURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(3 * 1000);
            final ByteArrayOutputStream output = new ByteArrayOutputStream();
            IOUtils.copy(conn.getInputStream(), output);
            String fileName = getfileNameByUrl(strUrl);

            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
            if (byteArrayInputStream == null) {
                return null;
            }
            uploadByOs(output, fileName);

            int available = byteArrayInputStream.available();
            CommonBizFile commonBizFile = new CommonBizFile();
            commonBizFile.setFileNames(getOrgFileNameByUrl(strUrl));
            commonBizFile.setUrls(fileName);
            commonBizFile.setFileSize((long) (available / 1024));
            commonBizFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
            commonBizFile.setUploadTime(LocalDateTime.now());
            return commonBizFile;
        } catch (Exception e) {
            log.error("getInputStreamByUrl 异常,exception is {}", e.getMessage(), e);
        } finally {
            try {
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
                log.error("getInputStreamByUrl 异常,exception is {}", e.getMessage(), e);
            }
        }
        return null;
    }

    // 从url获取附件上传
    private void uploadByOs(ByteArrayOutputStream output, String fileName) {

        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        String objectName = Objects.requireNonNull(fileName);

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(output.toByteArray()));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    public String getfileNameByUrl(String strUrl) {
        String s;
        //例: https://img.vip.imocq.com/000-7fa36f93-1f37-4d53-81b5-7fe14f6420b8/20230227/1677464812847_执照.jpg
        //例: https://file.vip.imocq.com/000-7fa36f93-1f37-4d53-81b5-7fe14f6420b8/20230227/1677464820671_注册资料.pdf?_upt=3577de901685600884
        if (strUrl.contains("?")) {
            // 1677464812847_执照.jpg
            s = strUrl.substring(strUrl.lastIndexOf("/"), strUrl.lastIndexOf("?"));
        } else {
            s = strUrl.substring(strUrl.lastIndexOf("/"));
        }
        // 执照.jpg
        String s2 = s.substring(s.lastIndexOf("_") + 1);

        return StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), s2);
    }

    public String getOrgFileNameByUrl(String strUrl) {
        String s;
        if (strUrl.contains("?")) {
            s = strUrl.substring(strUrl.lastIndexOf("/"), strUrl.lastIndexOf("?"));
        } else {
            s = strUrl.substring(strUrl.lastIndexOf("/"));
        }

        return s.substring(s.lastIndexOf("_") + 1);
    }

    // resolved: 移除导入导出功能耦合，应该由专门的导入导出服务处理
    // @Async
    // public void export(String fileName, CustomerInformationQuery query, Long progressId) {
    //     var progress = bizProgressMapper.selectById(progressId);
    //     TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
    //     List<String> noShowList = query.getNoShowList();
    //     Class<CustomerInformationListVO> customerInformationListVOClass = CustomerInformationListVO.class;
    //     try {
    //         IPage<CustomerInformationListVO> list = listPage(query);
    //         if (ObjectUtils.isEmpty(list.getRecords())) {
    //             throw new ServiceException("没有数据可导出");
    //         }
    //         if (!noShowList.isEmpty()) {
    //             for (String name : noShowList) {
    //                 notExport(customerInformationListVOClass, name, Excel.Type.IMPORT);
    //             }
    //         }
    //         var util = new ExcelUtil<>(CustomerInformationListVO.class);
    //         ByteArrayOutputStream output = new ByteArrayOutputStream();
    //         util.exportExcelToOss(output, list.getRecords(), "企业档案", "", fileName, endpoint, accessKeyId, accessKeySecret, bucketName);
    //         if (!noShowList.isEmpty()) {
    //             for (String name : noShowList) {
    //                 notExport(customerInformationListVOClass, name, Excel.Type.ALL);
    //             }
    //         }
    //         dataSourceTransactionManager.commit(transactionStatus);
    //         progress.setStatus(1);
    //         log.info("********************************导出成功****************************************************");
    //     } catch (Exception e) {
    //         log.info("********************************导出失败****************************************************");
    //         String message = e.getMessage();
    //         String regex = "[\u4e00-\u9fa5]+";
    //         if (!message.matches(".*" + regex + ".*")) {
    //             message = "未知异常请联系管理员:" + message;
    //         }
    //         progress.setReason(message);
    //         progress.setStatus(2);//导出失败
    //         log.error("导出失败", e);
    //         dataSourceTransactionManager.rollback(transactionStatus);
    //         throw new ServiceException(e.getMessage());
    //     } finally {
    //         bizProgressMapper.updateById(progress);
    //         if (!noShowList.isEmpty()) {
    //             for (String name : noShowList) {
    //                 try {
    //                     notExport(customerInformationListVOClass, name, Excel.Type.ALL);
    //                 } catch (NoSuchFieldException e) {
    //                     log.error("修改类注解参数 NoSuchFieldException {}", e.getMessage());
    //                 } catch (IllegalAccessException e) {
    //                     log.error("修改类注解参数 IllegalAccessException {}", e.getMessage());
    //                 }
    //             }
    //         }
    //     }
    // }

    /**
     * 不允许导出的字段
     */
    private void notExport(Class<?> cla, String fieldName, Excel.Type type) throws NoSuchFieldException, IllegalAccessException {
        Field field = cla.getDeclaredField(fieldName);
        Excel excel = field.getAnnotation(Excel.class);
        InvocationHandler excelInvocationHandler = Proxy.getInvocationHandler(excel);
        Field excelInvocationHandlerField = excelInvocationHandler.getClass().getDeclaredField("memberValues");
        excelInvocationHandlerField.setAccessible(true);
        Map<String, Excel.Type> map = (Map<String, Excel.Type>) excelInvocationHandlerField.get(excelInvocationHandler);
        map.put("type", type);
    }

    /**
     * 企业注销记录查询
     *
     * @param customerId 客户id
     * @return 注销记录查询
     */
    public CancellationRecordVO getCancellationRecord(Long customerId) {
        CancellationRecordVO cancellationRecordVO = new CancellationRecordVO();
        // 查询客户注销相关合同

        CustomerContract contract = customerContractMapper.getContractByProductNameAndCustomerId("注销", customerId);
        if (ObjectUtil.isNull(contract)) {
            return cancellationRecordVO;
        }
        // 合同
        Long contractId = contract.getContractId();
        SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(contract.getCreateBy()));

        cancellationRecordVO.setContractNo(contract.getContractNo());
        cancellationRecordVO.setContractTime(contract.getCreateTime());
        cancellationRecordVO.setContractUserName(Optional.ofNullable(sysUser).map(SysUser::getNickName).orElse(""));

        // resolved: 移除订单管理相关的逻辑
        // 工单
        // Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>()
        //         .eq(Order::getContractId, contractId)
        //         .orderByDesc(Order::getCreateTime)
        //         .last("limit 1"));
        // if (ObjectUtil.isNull(order)) {
        //     return cancellationRecordVO;
        // }
        cancellationRecordVO.setOrderNo(order.getOrderNo());
        cancellationRecordVO.setOrderTime(order.getOrderStatus().equals("1") ? order.getCompleteTime() : null);
        Long executor = order.getExecutor() == null ? Long.valueOf(order.getCreateBy()) : order.getExecutor();
        SysUser orderUser = sysUserMapper.selectUserById(executor);
        cancellationRecordVO.setOrderUserName(orderUser.getNickName());

        // resolved: 移除证书管理相关的业务逻辑
        // LicenseBizTask licenseBizTask = licenseBizTaskMapper.selectOne(new LambdaQueryWrapper<LicenseBizTask>()
        //         .eq(LicenseBizTask::getContractId, contractId)
        //         .orderByDesc(LicenseBizTask::getCreateTime)
        //         .last("limit 1"));
        //
        //
        // if (ObjectUtil.isNull(licenseBizTask)) {
        //     return cancellationRecordVO;
        // }
        // sysUser = sysUserMapper.selectUserById(licenseBizTask.getHandleUserId());
        // cancellationRecordVO.setTaskNo(licenseBizTask.getCode());
        // cancellationRecordVO.setTaskTime(licenseBizTask.getBizStatus().equals(LicenseBizStatusConstant.COMPLETED) ? licenseBizTask.getUpdateTime() : null);
        // cancellationRecordVO.setTaskUserName(Optional.ofNullable(sysUser).map(SysUser::getNickName).orElse(""));

        return cancellationRecordVO;
    }

    /**
     * 查询相关附件信息
     */
    public List<CommonBizFile> getRelateFiles(Long customerId) {
        List<CommonBizFile> commonBizFileList = new ArrayList<>();
        List<CommonBizFile> customerFileList = commonBizFileService.selectByMainIdAndBizType(customerId, null);
        if (ObjectUtil.isNotEmpty(customerFileList)) {
            commonBizFileList.addAll(customerFileList);
        }
        // resolved: 移除证书管理相关的许可证附件逻辑
        // 许可证附件添加
        // List<CustomerLicense> customerLicenseList = customerLicenseMapper.selectList(new LambdaQueryWrapper<CustomerLicense>()
        //         .eq(CustomerLicense::getCiId, customerId));
        // if (ObjectUtil.isNotEmpty(customerLicenseList)) {
        //     List<CommonBizFile> customerLicenseFileList = commonBizFileService.selectByMainIdAndBizType(customerLicenseList.stream()
        //             .map(CustomerLicense::getLicenseId)
        //             .toList(), BizType.CUSTOMER_LICENSE);
        //     commonBizFileList.addAll(customerLicenseFileList);
        // }
        // 根据上传时间 asc排序
        commonBizFileList.sort(Comparator.comparing(CommonBizFile::getUploadTime));
        return commonBizFileList;
    }

}
package com.jri.message.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.message.constants.MessageConstant;
import com.jri.message.domain.MessageDetail;
import com.jri.message.domain.MessageTemplate;
import com.jri.message.domain.query.ExpireDetailQuery;
import com.jri.message.domain.query.MessageDetailQuery;
import com.jri.message.domain.request.MessageDetailForm;
import com.jri.message.domain.vo.ExpireDetailVO;
import com.jri.message.domain.vo.ExtraDataVO;
import com.jri.message.domain.vo.MessageDetailVO;
import com.jri.message.mapper.MessageDetailMapper;
import com.jri.message.websocket.EventNotifyWebsocketHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class MessageDetailService extends ServiceImpl<MessageDetailMapper, MessageDetail> {

    private final MessageTemplateService messageTemplateService;

    private final EventNotifyWebsocketHandler eventNotifyWebsocketHandler;

    @Resource
    private MessageDetailMapper messageDetailMapper;


    /**
     * 发送消息
     *
     * @param form 消息表单
     * @return 消息是否成功发送
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean send(MessageDetailForm form) {
        String title = null;
        String content = null;
        if (form.getCode() != null) {
            MessageTemplate messageTemplate = messageTemplateService.getMessageTemplateByCode(form.getCode());
            if (messageTemplate != null) {
                title = messageTemplateService.processTemplate(messageTemplate.getTitle(), form.getTitleParam());
                if (!form.getCode().equals("contract_expiration")) {
                    content = messageTemplateService.processTemplate(messageTemplate.getContent(), form.getContentParam());
                } else if (form.getCode().equals("contract_expiration")) {
                    //查询当前的预警模板
                    if (StringUtils.isNotBlank(form.getAlertContent())) {
                        content = messageTemplateService.processTemplate(form.getAlertContent(), form.getContentParam());
                    }
                }
            }else {
                log.info("未找到对应消息模板");
                return false;
            }
        }


        List<Long> recipients = form.getRecipients();
        List<MessageDetail> messageDetailList = new ArrayList<>();

        for (Long recipient : recipients) {
            MessageDetail messageDetail = MessageDetail
                    .builder()
                    .id(null)
                    .bizType(form.getBizType())
                    .bizOrderId(form.getBizOrderId())
                    .recipient(recipient)
                    .sender(form.getNickName())
                    .readStatus(MessageConstant.UN_READ)
                    .title(title)
                    .content(content)
                    .receiveTime(LocalDateTime.now())
                    .createBy(form.getNickName())
                    .messageType(form.getMessageType())
                    .createTime(LocalDateTime.now())
                    .extraData(JSON.toJSONString(form.getExtraDataVO()))
                    .build();
            messageDetailList.add(messageDetail);
        }

        saveBatch(messageDetailList);
        log.info("成功插入{}条", messageDetailList.size());

        // resolved: 解耦WebSocket推送逻辑，改为事件发布模式
        //websocket推送消息
        // for (MessageDetail messageDetail : messageDetailList) {
        //     eventNotifyWebsocketHandler.sendMessageToUser(String.valueOf(messageDetail.getRecipient()), JSON.toJSONString(messageDetail));
        // }

        // TODO: 发布消息发送事件，由事件监听器处理WebSocket推送
        // applicationEventPublisher.publishEvent(new MessageSendEvent(messageDetailList));
        return true;
    }

    /**
     * 发送公告消息
     *
     * @param form 消息表单
     * @return 消息是否成功发送
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendToAllUser(MessageDetail form, List<Long> ids) {

        List<Long> recipients = ids;
        List<MessageDetail> messageDetailList = new ArrayList<>();

        for (Long recipient : recipients) {
            MessageDetail messageDetail = MessageDetail
                    .builder()
                    .id(null)
                    .bizType(form.getBizType())
                    .bizOrderId(form.getBizOrderId())
                    .recipient(recipient)
                    .sender(form.getSender())
                    .readStatus(MessageConstant.UN_READ)
                    .title(form.getTitle())
                    .content(form.getContent())
                    .receiveTime(LocalDateTime.now())
                    .createBy(form.getSender())
                    .messageType(form.getMessageType())
                    .createTime(LocalDateTime.now())
                    .extraData(JSON.toJSONString(form.getExtraData()))
                    .build();
            messageDetailList.add(messageDetail);
        }

        saveBatch(messageDetailList);
        log.info("成功插入{}条", messageDetailList.size());

        // resolved: 解耦WebSocket推送逻辑，改为事件发布模式
        //websocket推送消息
        // for (MessageDetail messageDetail : messageDetailList) {
        //     eventNotifyWebsocketHandler.sendMessageToUser(String.valueOf(messageDetail.getRecipient()), JSON.toJSONString(messageDetail));
        // }

        // TODO: 发布消息发送事件，由事件监听器处理WebSocket推送
        // applicationEventPublisher.publishEvent(new MessageSendEvent(messageDetailList));
        return true;
    }


    /**
     * 删除消息
     *
     * @param id 消息id
     * @return 删除是否成功
     */
    public Boolean remove(Long id) {
        return removeById(id);
    }

    public IPage<MessageDetailVO> listPage(MessageDetailQuery query) {
        var page = new Page<MessageDetailQuery>(query.getPageNum(), query.getPageSize());
        query.setRecipient(Objects.requireNonNull(SecurityUtils.getUserId()));
        IPage<MessageDetailVO> messageDetailVOIPage = getBaseMapper().listPage(query, page);
        if (messageDetailVOIPage != null && messageDetailVOIPage.getRecords() != null) {
            messageDetailVOIPage.getRecords().stream()
                    .filter(item -> item.getExtraData() != null)
                    .forEach(item -> {
                        ExtraDataVO extraDataVO = JSON.parseObject(item.getExtraData(), ExtraDataVO.class);
                        item.setExtraDataVO(extraDataVO);
                    });
        }

        return messageDetailVOIPage;
    }

    public Boolean read(List<Long> ids) {
        Long userId = Objects.requireNonNull(SecurityUtils.getUserId());
        return getBaseMapper().updateReadStatus(ids, userId);
    }

    public Integer getMessageNum(String readStatus) {
        Long userId = Objects.requireNonNull(SecurityUtils.getUserId());
        return getBaseMapper().selectMessageNum(userId, readStatus);
    }

    public MessageDetailVO getDetailById(Long id) {
        return getBaseMapper().selectDetailById(id);
    }


    public IPage<ExpireDetailVO> getExpireDetail(ExpireDetailQuery query) {
        var page = new Page<MessageDetailQuery>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().getExpireDetail(query, page);
    }

    public Boolean updateNotificationTitle(Long notificationId, String newTitle) {
        return getBaseMapper().updateNotificationTitle(notificationId, newTitle);
    }

    public String getUnRead() {
        //发消息有多少个未读消息
        MessageDetailQuery messageDetailQuery = new MessageDetailQuery();
        messageDetailQuery.setRecipient(SecurityUtils.getUserId());
        messageDetailQuery.setReadStatus(Long.valueOf(MessageConstant.UN_READ));
        var page = new Page<MessageDetailQuery>(1, -1);
        IPage<MessageDetailVO> messageDetailVOIPage = messageDetailMapper.listPage(messageDetailQuery, page);
        MessageTemplate messageTemplate = messageTemplateService.getMessageTemplateByCode("messgae_unread");
        if (messageTemplate!=null) {
            String size = "0";
            if (messageDetailVOIPage != null && StringUtils.isNotNull(messageDetailVOIPage.getRecords())) {
                size = String.valueOf(messageDetailVOIPage.getRecords().size());
            }
            Map<String, String> contentParams2 = new HashMap<>();
            contentParams2.put("unReadSize", size);
            String content = messageTemplateService.processTemplate(messageTemplate.getContent(), contentParams2);
            MessageDetail messageDetail = MessageDetail
                    .builder()
                    .id(null)
                    .bizType("message")
                    .recipient(SecurityUtils.getUserId())
                    .sender(MessageConstant.SYSTEM)
                    .title(messageTemplate.getTitle())
                    .content(content)
                    .receiveTime(LocalDateTime.now())
                    .createBy(MessageConstant.SYSTEM)
                    .messageType(MessageConstant.ALERT)
                    .createTime(LocalDateTime.now())
                    .build();
            return content;
        }
        return null;
    }

}

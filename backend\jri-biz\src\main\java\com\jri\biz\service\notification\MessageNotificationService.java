package com.jri.biz.service.notification;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.entity.notification.MessageNotification;
import com.jri.biz.domain.vo.notification.MessageNotificationListVO;
import com.jri.biz.domain.vo.notification.MessageNotificationVO;
import com.jri.biz.service.CommonBizFileService;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.R;
import com.jri.common.core.domain.entity.SysDept;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.bean.BeanUtils;
import com.jri.message.constants.MessageConstant;
import com.jri.message.domain.MessageDetail;
import com.jri.message.domain.query.MessageNotificationQuery;
import com.jri.message.domain.request.MessageDetailForm;
import com.jri.message.domain.request.MessageNotificationForm;
import com.jri.biz.mapper.notification.MessageNotificationMapper;
import com.jri.message.domain.vo.ExtraDataVO;
import com.jri.message.service.MessageDetailService;
import com.jri.message.websocket.EventNotifyWebsocketHandler;
import com.jri.system.mapper.SysUserMapper;
import com.jri.system.service.ISysDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Service
public class MessageNotificationService extends ServiceImpl<MessageNotificationMapper, MessageNotification> {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private MessageDetailService messageDetailService;

    @Resource
    private ISysDeptService deptService;

    @Resource
    private EventNotifyWebsocketHandler eventNotifyWebsocketHandler;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<MessageNotificationListVO> listPage(MessageNotificationQuery query) {
        var page = new Page<MessageNotificationListVO>(query.getPageNum(), query.getPageSize());
        IPage<MessageNotificationListVO> messageNotificationListVOIPage = getBaseMapper().listPage(query, page);
        if (messageNotificationListVOIPage != null && messageNotificationListVOIPage.getRecords() != null) {
            messageNotificationListVOIPage.getRecords().stream().forEach(item -> {
                List<String> deptNameListStr = new ArrayList<>();
                if (item.getScopeDepts() != null) {
                    String[] split = item.getScopeDepts().split(",");
                    for (String deptId : split) {
                        if (!deptId.isBlank()) {
                            Long id = Long.valueOf(deptId);
                            SysDept sysDept = deptService.selectDeptById(id);
                            deptNameListStr.add(sysDept.getDeptName());
                        }
                    }
                    item.setScopeDepts(deptNameListStr.stream().collect(Collectors.joining(",")));
                }
            });
        }
        return messageNotificationListVOIPage;
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public R<MessageNotificationVO> getDetailById(Long id) {
        MessageNotificationVO messageNotificationVO = new MessageNotificationVO();
        MessageNotification messageNotification = getById(id);
        if (id != null && StringUtils.isNull(messageNotification)) {
            return R.ok(null,"当前公告已删除");
        }
        BeanUtils.copyProperties(messageNotification, messageNotificationVO);

        List<CommonBizFile> commonBizFiles = commonBizFileService.selectByMainIdAndBizType(id, BizType.NOTIFICATION_FILE);
        messageNotificationVO.setNotificationFiles(commonBizFiles);
        String[] split = messageNotification.getScopeDepts().split(",");
        messageNotificationVO.setScopeDeptsList(Arrays.stream(split).toList());
        return R.ok(messageNotificationVO);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(MessageNotificationForm form) {
        MessageNotification messageNotification = new MessageNotification();
        Long oldId = form.getId();
        if (oldId != null) {
            //编辑 判断标题改变了没有 改变了消息中心也要变
            R<MessageNotificationVO> detailById = getDetailById(oldId);
            MessageNotificationVO data = detailById.getData();
            if (data != null && !data.getTitle().equals(form.getTitle())) {
             // 改变了消息中心也要变
                messageDetailService.updateNotificationTitle(oldId,form.getTitle());
            }
        }
        BeanUtils.copyProperties(form, messageNotification);
        if (form != null && form.getScopeDeptsList() != null) {
            List<String> scopeDeptsList = form.getScopeDeptsList();
            String scopeDepts = scopeDeptsList.stream().collect(Collectors.joining(","));
            messageNotification.setScopeDepts(scopeDepts);
        }
        boolean flag = saveOrUpdate(messageNotification);
        if (oldId == null) {
            //新增公告
            if (form.getScopeType().equals("2") && form.getScopeDeptsList() != null) {
                //指定部门用户
                List<Long> allUserIdsByDeptId = sysUserMapper.getAllUserIdsByDeptId(form.getScopeDeptsList().stream().mapToLong(Long::valueOf).boxed()
                        .toList());
                allUserIdsByDeptId.stream().forEach(item -> {
                    MessageDetail messageDetail = MessageDetail
                            .builder()
                            .bizType("notification")
                            .recipient(item)
                            .sender(MessageConstant.SYSTEM)
                            .readStatus(MessageConstant.UN_READ)
                            .title(form.getTitle())
                            .content(MessageConstant.NOTIFICATION_ALERT)
                            .receiveTime(LocalDateTime.now())
                            .notificationId(messageNotification.getId())
                            .createBy(MessageConstant.SYSTEM)
                            .messageType(MessageConstant.NOTIFICATION)
                            .createTime(LocalDateTime.now())
                            .build();
                    messageDetailService.save(messageDetail);
                    // resolved: 解耦WebSocket推送逻辑
                    // try {
                    //     eventNotifyWebsocketHandler.sendMessageToUser(String.valueOf(item), JSON.toJSONString(messageDetail));
                    // } catch (Exception e) {
                    //     log.error("消息发送失败:{}");
                    // }
                });
            } else {
                //全部
                //指定部门用户
                List<Long> allUserIdsByDeptId = sysUserMapper.getAllUserIds();
                allUserIdsByDeptId.stream().forEach(item -> {
                    MessageDetail messageDetail = MessageDetail
                            .builder()
                            .bizType("notification")
                            .recipient(item)
                            .sender(MessageConstant.SYSTEM)
                            .readStatus(MessageConstant.UN_READ)
                            .title(form.getTitle())
                            .content(MessageConstant.NOTIFICATION_ALERT)
                            .receiveTime(LocalDateTime.now())
                            .notificationId(messageNotification.getId())
                            .createBy(MessageConstant.SYSTEM)
                            .messageType(MessageConstant.NOTIFICATION)
                            .createTime(LocalDateTime.now())
                            .build();
                    messageDetailService.save(messageDetail);
                    // resolved: 解耦WebSocket推送逻辑
                    // try {
                    //     eventNotifyWebsocketHandler.sendMessageToUser(String.valueOf(item), JSON.toJSONString(messageDetail));
                    // } catch (Exception e) {
                    //     log.error("消息发送失败:{}");
                    // }
                });
            }

        }
        if (form != null && form.getNotificationFiles() != null) {
            commonBizFileService.deleteByMainIdAndBizType(form.getId(), BizType.NOTIFICATION_FILE);
            List<CommonBizFile> notificationFiles = form.getNotificationFiles();
            if (ObjectUtil.isNotEmpty(notificationFiles)) {
                notificationFiles.stream().forEach(item -> {
                    item.setMainId(messageNotification.getId());
                    item.setBizType(BizType.NOTIFICATION_FILE);
                    commonBizFileService.save(item);
                });
            }
        }

        return flag;
    }

//    /**
//    * 修改
//    *
//    * @param form 表单
//    * @return 结果
//    */
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean update(MessageNotificationForm form) {
//        // todo 完善新增/更新逻辑
//        MessageNotification messageNotification = MessageNotificationConvert.INSTANCE.convert(form);
//        return updateById(messageNotification);
//    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        commonBizFileService.deleteByMainIdAndBizType(id, BizType.NOTIFICATION_FILE);
        return removeById(id);
    }
}
